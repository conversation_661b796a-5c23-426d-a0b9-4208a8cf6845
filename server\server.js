import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { testConnection } from './config/db.js';

// Import routes
import userRoutes from './routes/users.routes.js';
import rolesRoutes from './routes/roles.routes.js';
import dashboardRoutes from './routes/dashboard.routes.js';
import adminRoutes from './routes/admin.routes.js';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 8000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' })); // Increased limit for image uploads
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    const dbConnected = await testConnection();
    res.json({ 
      success: true, 
      message: 'API is running',
      database: dbConnected ? 'connected' : 'disconnected',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      success: false,
      message: 'Health check failed',
      error: error.message
    });
  }
});

// API Routes
console.log('🔧 Registering API routes...');
app.use('/api/users', userRoutes);
app.use('/api/auth', userRoutes); // For backward compatibility with login
app.use('/api/roles', rolesRoutes);
app.use('/api/dashboard', dashboardRoutes);
console.log('🔧 Registering admin routes...');
app.use('/api/admin', adminRoutes); // Unified admin login
console.log('✅ All routes registered');

// API Documentation endpoint
app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: 'My Group API Server',
    version: '1.0.0',
    endpoints: {
      health: 'GET /api/health',
      auth: {
        customerLogin: 'POST /api/auth/login'
      },
      admin: {
        unifiedLogin: 'POST /api/admin/login',
        getRoles: 'GET /api/admin/roles',
        getUsers: 'GET /api/admin/users'
      },
      users: {
        create: 'POST /api/users',
        getAll: 'GET /api/users',
        getById: 'GET /api/users/:userId',
        updateDetails: 'PUT /api/users/update-details',
        updateStatus: 'PUT /api/users/:userId/status',
        delete: 'DELETE /api/users/:userId',
        getByCreator: 'GET /api/users/creator/:creatorId',
        changePassword: 'PUT /api/users/:userId/change-password'
      },
      roles: {
        auth: {
          adminLogin: 'POST /api/roles/admin/login',
          corporateLogin: 'POST /api/roles/corporate/login',
          headOfficeLogin: 'POST /api/roles/headoffice/login',
          regionalLogin: 'POST /api/roles/regional/login',
          branchLogin: 'POST /api/roles/branch/login'
        },
        management: {
          createUser: 'POST /api/roles/users',
          getUsersByRole: 'GET /api/roles/users/:role',
          updateUser: 'PUT /api/roles/users/:userId',
          deleteUser: 'DELETE /api/roles/users/:userId'
        },
        hierarchy: 'GET /api/roles/hierarchy/:role',
        dashboard: 'GET /api/roles/dashboard'
      },
      dashboard: {
        generic: 'GET /api/dashboard',
        admin: 'GET /api/dashboard/admin',
        corporate: 'GET /api/dashboard/corporate',
        headoffice: 'GET /api/dashboard/headoffice',
        regional: 'GET /api/dashboard/regional',
        branch: 'GET /api/dashboard/branch',
        user: 'GET /api/dashboard/user',
        partner: 'GET /api/dashboard/partner'
      }
    }
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err.stack);
  res.status(500).json({ 
    success: false, 
    error: 'Something went wrong!',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    path: req.originalUrl,
    method: req.method
  });
});

// Start server
const startServer = async () => {
  try {
    // Test database connection
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.error('❌ Failed to connect to database. Server will not start.');
      process.exit(1);
    }

    // Start the server
    app.listen(PORT, () => {
      console.log('🚀 ================================');
      console.log(`🚀 Server running on port ${PORT}`);
      console.log(`🚀 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🚀 Health check: http://localhost:${PORT}/api/health`);
      console.log(`🚀 API docs: http://localhost:${PORT}/api`);
      console.log('🚀 ================================');
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🔄 SIGTERM received. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🔄 SIGINT received. Shutting down gracefully...');
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
startServer();
