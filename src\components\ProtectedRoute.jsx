import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const ProtectedRoute = ({ children, allowedRoles = [] }) => {
  const { currentUser } = useAuth();

  if (!currentUser) {
    return <Navigate to="/admin" replace />;
  }

  if (allowedRoles.length > 0 && !allowedRoles.includes(currentUser.role_name)) {
    // Redirect to appropriate dashboard based on user role
    switch (currentUser.role_name) {
      case 'admin':
        return <Navigate to="/admin/dashboard" replace />;
      case 'corporate':
        return <Navigate to="/corporate/dashboard" replace />;
      default:
        return <Navigate to="/" replace />;
    }
  }

  return children;
};

export default ProtectedRoute;
