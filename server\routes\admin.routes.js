import express from 'express';
import AdminController from '../controllers/admin.controller.js';

const router = express.Router();

// Test route
router.get('/test', (req, res) => {
  res.json({ success: true, message: 'Admin routes are working!' });
});

// Unified admin login route
router.post('/login', AdminController.adminLogin);

// Get all roles
router.get('/roles', AdminController.getRoles);

// Get all users
router.get('/users', AdminController.getUsers);

export default router;
