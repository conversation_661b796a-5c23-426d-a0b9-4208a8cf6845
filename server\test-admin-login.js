import { pool } from './config/db.js';
import bcrypt from 'bcryptjs';

// Test script to check database and create admin user if needed
async function testAdminLogin() {
  try {
    console.log('🔍 Checking database tables...');
    
    // Check if tables exist
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND (table_name = 'my_users' OR table_name = 'my_role')
      ORDER BY table_name;
    `;
    
    const tablesResult = await pool.query(tablesQuery);
    console.log('📋 Available tables:', tablesResult.rows.map(r => r.table_name));
    
    // Check roles
    console.log('\n🎭 Checking roles...');
    const rolesQuery = 'SELECT * FROM my_role ORDER BY id';
    const rolesResult = await pool.query(rolesQuery);
    console.log('Available roles:', rolesResult.rows);
    
    // Check users
    console.log('\n👥 Checking users...');
    const usersQuery = `
      SELECT 
        u.id, u.user_name, u.email, u.role_id, u.status,
        r.role as role_name, r.role_description
      FROM my_users u
      LEFT JOIN my_role r ON u.role_id = r.id
      ORDER BY u.id
    `;
    const usersResult = await pool.query(usersQuery);
    console.log('Available users:', usersResult.rows);
    
    // Create admin user if none exists
    if (usersResult.rows.length === 0) {
      console.log('\n🔧 No users found. Creating admin user...');
      
      // First, ensure we have an admin role
      let adminRoleId = 1;
      const adminRoleCheck = await pool.query("SELECT id FROM my_role WHERE role ILIKE '%admin%' LIMIT 1");
      if (adminRoleCheck.rows.length > 0) {
        adminRoleId = adminRoleCheck.rows[0].id;
      } else {
        // Create admin role if it doesn't exist
        const createRoleQuery = `
          INSERT INTO my_role (id, role, role_description) 
          VALUES (1, 'Admin', 'System Administrator') 
          ON CONFLICT (id) DO NOTHING
          RETURNING id
        `;
        const roleResult = await pool.query(createRoleQuery);
        adminRoleId = roleResult.rows[0]?.id || 1;
      }
      
      // Create admin user
      const hashedPassword = await bcrypt.hash('admin123', 10);
      const createUserQuery = `
        INSERT INTO my_users (
          user_name, password, role_id, group_id, email, mobile_number,
          ip_address, created_on, modified_on, last_logged_in, status
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW(), NOW(), 1)
        RETURNING id, user_name, email
      `;
      
      const userResult = await pool.query(createUserQuery, [
        'admin',
        hashedPassword,
        adminRoleId,
        1, // default group_id
        '<EMAIL>',
        '******-0000',
        '127.0.0.1'
      ]);
      
      console.log('✅ Admin user created:', userResult.rows[0]);
      console.log('📝 Login credentials:');
      console.log('   Username: admin');
      console.log('   Password: admin123');
    }
    
    console.log('\n🚀 Test complete! You can now login at:');
    console.log('   Frontend: http://localhost:3000/admin');
    console.log('   Backend API: http://localhost:8000/api/admin/login');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await pool.end();
  }
}

// Run the test
testAdminLogin();
