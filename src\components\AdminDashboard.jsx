import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import '../styles/AdminDashboard.css';

const AdminDashboard = () => {
  const { currentUser, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="admin-dashboard">
      <div className="dashboard-header">
        <div className="header-content">
          <h1>Admin Dashboard</h1>
          <div className="user-info">
            <span>Welcome, {currentUser?.username}</span>
            <button onClick={handleLogout} className="logout-btn">
              Logout
            </button>
          </div>
        </div>
      </div>

      <div className="dashboard-content">
        <div className="dashboard-grid">
          <div className="dashboard-card">
            <h3>User Management</h3>
            <p>Manage system users and their roles</p>
            <div className="card-stats">
              <span className="stat-number">0</span>
              <span className="stat-label">Total Users</span>
            </div>
          </div>

          <div className="dashboard-card">
            <h3>Corporate Management</h3>
            <p>Manage corporate users and permissions</p>
            <div className="card-stats">
              <span className="stat-number">0</span>
              <span className="stat-label">Corporate Users</span>
            </div>
          </div>

          <div className="dashboard-card">
            <h3>System Settings</h3>
            <p>Configure system-wide settings</p>
            <div className="card-stats">
              <span className="stat-number">Active</span>
              <span className="stat-label">System Status</span>
            </div>
          </div>

          <div className="dashboard-card">
            <h3>Reports</h3>
            <p>View system reports and analytics</p>
            <div className="card-stats">
              <span className="stat-number">0</span>
              <span className="stat-label">Reports Generated</span>
            </div>
          </div>
        </div>

        <div className="recent-activity">
          <h3>Recent Activity</h3>
          <div className="activity-list">
            <div className="activity-item">
              <span className="activity-time">Just now</span>
              <span className="activity-text">Admin {currentUser?.username} logged in</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
