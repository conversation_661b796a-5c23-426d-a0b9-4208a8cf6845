import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import '../styles/AdminLogin.css';

const AdminLogin = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const navigate = useNavigate();
  const { login } = useAuth();

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError(''); // Clear error when user types
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Validate form
      if (!formData.username.trim()) {
        throw new Error('Username is required');
      }
      if (!formData.password) {
        throw new Error('Password is required');
      }

      // Attempt login
      const result = await login(formData.username.trim(), formData.password);

      if (result.success) {
        // Redirect based on role
        const role = result.user.role_name;
        if (role === 'admin') {
          navigate('/admin/dashboard');
        } else if (role === 'corporate') {
          navigate('/corporate/dashboard');
        } else {
          throw new Error(`Access denied. Role '${role}' is not authorized for admin portal.`);
        }
      } else {
        throw new Error(result.error || 'Login failed');
      }

    } catch (error) {
      console.error('Login error:', error);
      setError(error.message || 'Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="admin-login-container">
      <div className="login-background">
        <div className="background-pattern"></div>
      </div>
      
      <div className="login-content">
        <div className="login-card">
          <div className="login-header">
            <div className="logo-section">
              <div className="logo">
                <span className="logo-icon">🏢</span>
                <span className="logo-text">MyGroup</span>
              </div>
              <h1>Admin Portal</h1>
              <p>Sign in to access your dashboard</p>
            </div>
          </div>

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="login-form">
            {error && (
              <div className="error-message">
                <span className="error-icon">⚠️</span>
                {error}
              </div>
            )}

            <div className="form-group">
              <label htmlFor="username">Username</label>
              <input
                type="text"
                id="username"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                placeholder="Enter your username"
                required
                disabled={loading}
              />
            </div>

            <div className="form-group">
              <label htmlFor="password">Password</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Enter your password"
                required
                disabled={loading}
              />
            </div>

            <button 
              type="submit" 
              className="login-button"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="loading-spinner"></span>
                  Signing in...
                </>
              ) : (
                'Sign In'
              )}
            </button>
          </form>

          {/* Default Credentials Info */}
          <div className="default-credentials">
            <h4>Default Admin Credentials:</h4>
            <div className="credentials-info">
              <p><strong>Username:</strong> admin</p>
              <p><strong>Password:</strong> admin123</p>
            </div>
            <small>Use these credentials for initial admin access</small>
          </div>

          <div className="login-footer">
            <div className="navigation-links">
              <a href="/">← Back to Home</a>
            </div>
            <div className="system-info">
              <small>MyGroup Admin Portal v1.0</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminLogin;
