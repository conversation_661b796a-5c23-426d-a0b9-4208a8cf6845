import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import HomScreen from './components/HomeScreen';
import AuthPage from './components/AuthPage';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import UnifiedLogin from './components/UnifiedLogin';
import UnifiedAdminLogin from './components/UnifiedAdminLogin';
import BackendDashboardLogin from './components/BackendDashboardLogin';
import PartnerLogin from './components/PartnerLogin';
import CustomerFrontend from './components/CustomerFrontend';
import AdminDashboard from './components/AdminDashboard';
import DefaultDashboard from './components/DefaultDashboard';
import SuperAdminDashboard from './components/SuperAdminDashboard';
import CorporateDashboard from './components/CorporateDashboard';
import HeadOfficeDashboard from './components/HeadOfficeDashboard';
import RegionalDashboard from './components/RegionalDashboard';
import BranchDashboard from './components/BranchDashboard';
import SchemaBasedUserManagement from './components/SchemaBasedUserManagement';
import ProtectedRoute from './components/ProtectedRoute';


// Component to handle root route redirection
const RootRedirect = () => {
  const { currentUser } = useAuth();
   if (!currentUser) {
    return <AuthPage />;
  }
  // Always show customer frontend at root
  return <HomScreen />;
};

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            {/* Root route - Customer Frontend */}
            <Route path="/" element={<RootRedirect />} />

            {/* Backend Dashboard Login - Admin, Corporate, Head Office, Regional, Branch */}
            <Route path="/login" element={<BackendDashboardLogin />} />

            {/* Partner Login */}
            <Route path="/partner" element={<PartnerLogin />} />

            {/* Legacy routes */}
            <Route path="/admin" element={<UnifiedAdminLogin />} />
            <Route path="/unified-login" element={<UnifiedLogin />} />

            {/* New Admin Dashboard Route */}
            <Route
              path="/admin-dashboard"
              element={
                <ProtectedRoute allowedRoles={['SUPER_ADMIN', 'CORPORATE', 'BRANCH']}>
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />

            {/* Default Dashboard for authenticated users */}
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <DefaultDashboard />
                </ProtectedRoute>
              }
            />

            {/* Super Admin Dashboard */}
            <Route
              path="/super-admin"
              element={
                <ProtectedRoute allowedRoles={['SUPER_ADMIN']}>
                  <SuperAdminDashboard />
                </ProtectedRoute>
              }
            />

            {/* Corporate Dashboard */}
            <Route
              path="/corporate"
              element={
                <ProtectedRoute allowedRoles={['CORPORATE']}>
                  <CorporateDashboard />
                </ProtectedRoute>
              }
            />

            {/* Head Office Dashboard */}
            <Route
              path="/head-office"
              element={
                <ProtectedRoute allowedRoles={['HEAD_OFFICE']}>
                  <HeadOfficeDashboard />
                </ProtectedRoute>
              }
            />

            {/* Regional Dashboard */}
            <Route
              path="/regional"
              element={
                <ProtectedRoute allowedRoles={['REGIONAL']}>
                  <RegionalDashboard />
                </ProtectedRoute>
              }
            />

            {/* Branch Dashboard */}
            <Route
              path="/branch"
              element={
                <ProtectedRoute allowedRoles={['BRANCH']}>
                  <BranchDashboard />
                </ProtectedRoute>
              }
            />

            {/* User Management - Schema Based */}
            <Route
              path="/admin/users"
              element={
                <ProtectedRoute allowedRoles={['admin', 'app_admin', 'corporate']}>
                  <SchemaBasedUserManagement />
                </ProtectedRoute>
              }
            />

            {/* Dashboard Routes for Schema-Based Roles */}
            <Route
              path="/admin/dashboard"
              element={
                <ProtectedRoute allowedRoles={['admin', 'app_admin']}>
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path="/corporate/dashboard"
              element={
                <ProtectedRoute allowedRoles={['corporate']}>
                  <CorporateDashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path="/headoffice/dashboard"
              element={
                <ProtectedRoute allowedRoles={['headoffice']}>
                  <HeadOfficeDashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path="/regional/dashboard"
              element={
                <ProtectedRoute allowedRoles={['regional']}>
                  <RegionalDashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path="/branch/dashboard"
              element={
                <ProtectedRoute allowedRoles={['branch']}>
                  <BranchDashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path="/partner/dashboard"
              element={
                <ProtectedRoute allowedRoles={['partner']}>
                  <DefaultDashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path="/customer/dashboard"
              element={
                <ProtectedRoute allowedRoles={['customer']}>
                  <CustomerFrontend />
                </ProtectedRoute>
              }
            />

            {/* Catch all route - redirect to root */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
