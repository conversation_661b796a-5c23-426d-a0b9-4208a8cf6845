import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import HomeScreen from './components/HomeScreen';
import AdminLogin from './components/AdminLogin';
import AdminDashboard from './components/AdminDashboard';
import CorporateDashboard from './components/CorporateDashboard';
import ProtectedRoute from './components/ProtectedRoute';
import { AuthProvider } from './contexts/AuthContext';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            {/* Root route - Customer Home Screen */}
            <Route path="/" element={<HomeScreen />} />

            {/* Admin Login */}
            <Route path="/admin" element={<AdminLogin />} />

            {/* Admin Dashboard - Protected Route */}
            <Route
              path="/admin/dashboard"
              element={
                <ProtectedRoute allowedRoles={['admin']}>
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />

            {/* Corporate Dashboard - Protected Route */}
            <Route
              path="/corporate/dashboard"
              element={
                <ProtectedRoute allowedRoles={['corporate']}>
                  <CorporateDashboard />
                </ProtectedRoute>
              }
            />

            {/* Catch all route - redirect to root */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
