import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import '../styles/CorporateDashboard.css';

const CorporateDashboard = () => {
  const { currentUser, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="corporate-dashboard">
      <div className="dashboard-header">
        <div className="header-content">
          <h1>Corporate Dashboard</h1>
          <div className="user-info">
            <span>Welcome, {currentUser?.username}</span>
            <button onClick={handleLogout} className="logout-btn">
              Logout
            </button>
          </div>
        </div>
      </div>

      <div className="dashboard-content">
        <div className="dashboard-grid">
          <div className="dashboard-card">
            <h3>Head Office Management</h3>
            <p>Manage head office operations and staff</p>
            <div className="card-stats">
              <span className="stat-number">0</span>
              <span className="stat-label">Head Offices</span>
            </div>
          </div>

          <div className="dashboard-card">
            <h3>Regional Management</h3>
            <p>Oversee regional operations and branches</p>
            <div className="card-stats">
              <span className="stat-number">0</span>
              <span className="stat-label">Regional Offices</span>
            </div>
          </div>

          <div className="dashboard-card">
            <h3>Branch Management</h3>
            <p>Monitor and manage branch operations</p>
            <div className="card-stats">
              <span className="stat-number">0</span>
              <span className="stat-label">Branch Offices</span>
            </div>
          </div>

          <div className="dashboard-card">
            <h3>User Management</h3>
            <p>Manage corporate users and permissions</p>
            <div className="card-stats">
              <span className="stat-number">0</span>
              <span className="stat-label">Corporate Users</span>
            </div>
          </div>
        </div>

        <div className="recent-activity">
          <h3>Recent Activity</h3>
          <div className="activity-list">
            <div className="activity-item">
              <span className="activity-time">Just now</span>
              <span className="activity-text">Corporate user {currentUser?.username} logged in</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
    { id: 'dashboard', label: 'Dashboard', icon: 'fas fa-tachometer-alt' },
    {
      id: 'office-management',
      label: 'Office Management',
      icon: 'fas fa-building',
      hasSubmenu: true,
      submenu: [
        { id: 'head-office', label: 'Head Office Login' },
        { id: 'regional-office', label: 'Regional Office Login' },
        { id: 'branch-office', label: 'Branch Office Login' }
      ]
    },
    {
      id: 'ads-management',
      label: 'Advertisement Management',
      icon: 'fas fa-ad',
      hasSubmenu: true,
      submenu: [
        { id: 'header-ads', label: 'Header Ads' },
        { id: 'ads-pricing', label: 'Ads Pricing' },
        { id: 'main-page-ads', label: 'Main Page Ads' }
      ]
    },
    { id: 'franchise-terms', label: 'Franchise Terms', icon: 'fas fa-file-contract' },
    {
      id: 'content-management',
      label: 'Content Management',
      icon: 'fas fa-edit',
      hasSubmenu: true,
      submenu: [
        { id: 'footer-management', label: 'Footer Management' }
      ]
    },
    { id: 'public-database', label: 'Public Database', icon: 'fas fa-database' },
    { id: 'change-password', label: 'Change Password', icon: 'fas fa-key' },
    { id: 'logout', label: 'Logout', icon: 'fas fa-sign-out-alt' }
  ];

  const handleSidebarClick = (itemId) => {
    if (itemId === 'logout') {
      logout();
      return;
    }
    setActiveSection(itemId);
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <div>
            <div className="d-flex justify-content-between align-items-center mb-4">
              <h2>Corporate Dashboard</h2>
              <Badge bg="info">Corporate Manager</Badge>
            </div>

            <Row className="mb-4">
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-building fa-2x text-primary mb-2"></i>
                    <h3 className="text-primary">12</h3>
                    <p className="mb-0">Head Offices</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-map-marker-alt fa-2x text-success mb-2"></i>
                    <h3 className="text-success">45</h3>
                    <p className="mb-0">Regional Offices</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-store fa-2x text-info mb-2"></i>
                    <h3 className="text-info">156</h3>
                    <p className="mb-0">Branch Offices</p>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={3}>
                <Card className="text-center dashboard-card">
                  <Card.Body>
                    <i className="fas fa-ad fa-2x text-warning mb-2"></i>
                    <h3 className="text-warning">28</h3>
                    <p className="mb-0">Active Ads</p>
                  </Card.Body>
                </Card>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Card>
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-chart-line me-2"></i>Quick Stats
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <div className="d-flex justify-content-between mb-2">
                      <span>Header Ads:</span>
                      <Badge bg="secondary">8</Badge>
                    </div>
                    <div className="d-flex justify-content-between mb-2">
                      <span>Main Page Ads:</span>
                      <Badge bg="secondary">3</Badge>
                    </div>
                    <div className="d-flex justify-content-between mb-2">
                      <span>Footer Sections:</span>
                      <Badge bg="secondary">12</Badge>
                    </div>
                    <div className="d-flex justify-content-between">
                      <span>Public Database Records:</span>
                      <Badge bg="success">2,456</Badge>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
              <Col md={6}>
                <Card>
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-tasks me-2"></i>Recent Activities
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <div className="activity-item mb-2">
                      <small className="text-muted">1 hour ago</small>
                      <div>New head office user created</div>
                    </div>
                    <div className="activity-item mb-2">
                      <small className="text-muted">3 hours ago</small>
                      <div>Header ad updated</div>
                    </div>
                    <div className="activity-item mb-2">
                      <small className="text-muted">5 hours ago</small>
                      <div>Ads pricing modified</div>
                    </div>
                    <div className="activity-item">
                      <small className="text-muted">1 day ago</small>
                      <div>Footer content updated</div>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          </div>
        );

      case 'head-office':
        return <HeadOfficeManagement />;

      case 'regional-office':
        return <RegionalOfficeManagement />;

      case 'branch-office':
        return <BranchOfficeManagement />;

      case 'header-ads':
        return <HeaderAdsManagement />;

      case 'ads-pricing':
        return <AdsPricingManagement />;

      case 'main-page-ads':
        return <MainPageAdsManagement />;

      case 'franchise-terms':
        return <FranchiseTermsManagement />;

      case 'footer-management':
        return <FooterManagement />;

      case 'public-database':
        return <PublicDatabaseManagement />;

      case 'change-password':
        return <ChangePasswordManagement />;

      default:
        return (
          <div className="text-center py-5">
            <i className="fas fa-building fa-3x text-muted mb-3"></i>
            <h3>Welcome to Corporate Dashboard</h3>
            <p className="text-muted">Select a menu item to get started</p>
          </div>
        );
    }
  };

  return (
    <div className="corporate-dashboard">
      <div className="d-flex">
        {/* Sidebar */}
        <div className="corporate-sidebar">
          <div className="sidebar-header">
            <h4>Corporate</h4>
            <small className="text-muted">{currentUser?.name}</small>
          </div>
          <div className="sidebar-menu">
            {sidebarItems.map(item => (
              <div key={item.id}>
                <div
                  className={`menu-item ${activeSection === item.id || activeSection.startsWith(item.id) ? 'active' : ''}`}
                  onClick={() => handleSidebarClick(item.id)}
                >
                  <i className={`${item.icon} me-2`}></i>
                  <span className="menu-label">{item.label}</span>
                  {item.hasSubmenu && <span className="submenu-arrow">▼</span>}
                </div>
                {item.hasSubmenu && item.submenu && (
                  <div className="submenu">
                    {item.submenu.map(subItem => (
                      <div
                        key={subItem.id}
                        className={`submenu-item ${activeSection === subItem.id ? 'active' : ''}`}
                        onClick={() => handleSidebarClick(subItem.id)}
                      >
                        <span className="submenu-label">{subItem.label}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="corporate-content">
          <div className="content-header">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <span>Corporate Dashboard</span>
                <span className="mx-2">|</span>
                <span>Management</span>
              </div>
              <div className="d-flex align-items-center">
                <NotificationSystem
                  userId={currentUser?.id}
                  userRole={currentUser?.role}
                />
                <span className="ms-3">Welcome, {currentUser?.name || currentUser?.username}</span>
              </div>
            </div>
          </div>

          <div className="content-body">
            {success && <Alert variant="success" dismissible onClose={() => setSuccess('')}>{success}</Alert>}
            {error && <Alert variant="danger" dismissible onClose={() => setError('')}>{error}</Alert>}

            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CorporateDashboard;
