import { pool } from '../config/db.js';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

class AdminController {
  // Unified admin login that checks my_users and my_role tables
  static async adminLogin(req, res) {
    try {
      const { username, password } = req.body;
      
      if (!username || !password) {
        return res.status(400).json({
          success: false,
          error: 'Username and password are required'
        });
      }
      
      // Query to get user with role information
      const query = `
        SELECT 
          u.id,
          u.user_name,
          u.password,
          u.role_id,
          u.email,
          u.mobile_number,
          u.status,
          r.id as role_id_check,
          r.role as role_name,
          r.role_description
        FROM my_users u
        INNER JOIN my_role r ON u.role_id = r.id
        WHERE u.user_name = $1 AND u.status = 1
      `;
      
      const result = await pool.query(query, [username]);
      
      if (result.rows.length === 0) {
        return res.status(401).json({
          success: false,
          error: 'Invalid username or password'
        });
      }
      
      const user = result.rows[0];
      
      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          error: 'Invalid username or password'
        });
      }
      
      // Generate JWT token
      const token = jwt.sign(
        { 
          userId: user.id, 
          username: user.user_name,
          roleId: user.role_id,
          roleName: user.role_name
        },
        process.env.JWT_SECRET || 'fallback_secret',
        { expiresIn: '24h' }
      );
      
      // Determine redirect URL based on role
      let redirectUrl = '/dashboard'; // default
      const roleName = user.role_name.toLowerCase();
      
      switch (roleName) {
        case 'admin':
        case 'super admin':
        case 'administrator':
          redirectUrl = '/admin/dashboard';
          break;
        case 'corporate':
        case 'corporate manager':
          redirectUrl = '/corporate/dashboard';
          break;
        case 'head office':
        case 'head office manager':
          redirectUrl = '/headoffice/dashboard';
          break;
        case 'regional':
        case 'regional manager':
          redirectUrl = '/regional/dashboard';
          break;
        case 'branch':
        case 'branch manager':
          redirectUrl = '/branch/dashboard';
          break;
        case 'user':
        case 'customer':
          redirectUrl = '/user/dashboard';
          break;
        case 'partner':
          redirectUrl = '/partner/dashboard';
          break;
        case 'reports':
          redirectUrl = '/reports/dashboard';
          break;
        default:
          redirectUrl = '/dashboard';
      }
      
      // Update last login time
      await pool.query(
        'UPDATE my_users SET last_logged_in = NOW() WHERE id = $1',
        [user.id]
      );
      
      // Return success response
      res.json({
        success: true,
        message: 'Login successful',
        token,
        user: {
          id: user.id,
          username: user.user_name,
          email: user.email,
          mobile_number: user.mobile_number,
          role_id: user.role_id,
          role_name: user.role_name,
          role_description: user.role_description
        },
        redirectUrl
      });
      
    } catch (error) {
      console.error('Admin login error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
  
  // Get all roles for reference
  static async getRoles(req, res) {
    try {
      const query = 'SELECT * FROM my_role ORDER BY id';
      const result = await pool.query(query);
      
      res.json({
        success: true,
        roles: result.rows
      });
    } catch (error) {
      console.error('Get roles error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
  
  // Get all users with their roles
  static async getUsers(req, res) {
    try {
      const query = `
        SELECT 
          u.id,
          u.user_name,
          u.email,
          u.mobile_number,
          u.status,
          u.created_on,
          u.last_logged_in,
          r.role as role_name,
          r.role_description
        FROM my_users u
        INNER JOIN my_role r ON u.role_id = r.id
        ORDER BY u.created_on DESC
      `;
      
      const result = await pool.query(query);
      
      res.json({
        success: true,
        users: result.rows
      });
    } catch (error) {
      console.error('Get users error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
}

export default AdminController;
